import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { DBOS } from "@dbos-inc/dbos-sdk";
import { LLMService } from './LLMService';
import { ChatResponse } from '../models/types';
import config from '../config';

export class GeminiLLMService extends LLMService {
  private genAI: GoogleGenerativeAI;

  constructor() {
    super();
    this.genAI = new GoogleGenerativeAI(config.llm.apiKey);
  }

  @DBOS.step()
  async generate(systemPrompt: string, userPrompt: string): Promise<ChatResponse> {
    const model = this.genAI.getGenerativeModel({ model: config.llm.model });

    const chat = model.startChat({
      history: [
        {
          role: "user",
          parts: [{ text: systemPrompt }],
        },
        {
          role: "model",
          parts: [{ text: "Okay, I'm ready. What's the user's request?" }],
        },
      ],
      generationConfig: {
        maxOutputTokens: config.llm.maxTokens,
        responseMimeType: "application/json",
      },
    });

    const result = await chat.sendMessage(userPrompt);
    const response = result.response;
    const text = response.text();

    DBOS.logger.info(`Raw LLM response text: ${text}`);

    try {
      const parsed = JSON.parse(text);
      DBOS.logger.info(`Parsed LLM response: ${JSON.stringify(parsed)}`);
      
      // Validate response structure
      if (!this.isValidChatResponse(parsed)) {
        throw new Error('Invalid response structure from LLM');
      }
      
      return parsed;
    } catch (error) {
      DBOS.logger.error(`Failed to parse LLM response as JSON: ${(error as Error).message}`);
      DBOS.logger.error(`Raw response was: ${text}`);
      throw new Error(`Invalid JSON response from LLM: ${(error as Error).message}`);
    }
  }

  private isValidChatResponse(obj: any): obj is ChatResponse {
    return (
      obj &&
      typeof obj === 'object' &&
      Array.isArray(obj.reply) &&
      obj.reply.length > 0 &&
      Array.isArray(obj.skills) &&
      typeof obj.theme === 'string' &&
      obj.reply.every((msg: any) => 
        typeof msg.character === 'string' &&
        typeof msg.text === 'string' &&
        typeof msg.delay === 'number'
      )
    );
  }
}
