import { DBOS } from '@dbos-inc/dbos-sdk';
import * as fs from 'fs/promises';
import * as path from 'path';

export class PromptService {
  private static promptCache: Map<string, string> = new Map();

  @DBOS.step()
  static async getSystemPrompt(promptName: string = 'agent_system'): Promise<string> {
    // Check cache first
    if (this.promptCache.has(promptName)) {
      return this.promptCache.get(promptName)!;
    }

    const filePath = path.join(__dirname, '..', '..', 'prompts', `${promptName}.md`);
    
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      
      // Cache the prompt for future use
      this.promptCache.set(promptName, content);
      
      return content;
    } catch (error) {
      DBOS.logger.error(`Failed to read prompt file ${filePath}: ${(error as Error).message}`);
      throw new Error(`Prompt file not found: ${promptName}`);
    }
  }

  @DBOS.step()
  static async getAvailablePrompts(): Promise<string[]> {
    const promptsDir = path.join(__dirname, '..', '..', 'prompts');
    
    try {
      const files = await fs.readdir(promptsDir);
      return files
        .filter(file => file.endsWith('.md'))
        .map(file => file.replace('.md', ''));
    } catch (error) {
      DBOS.logger.error(`Failed to read prompts directory: ${(error as Error).message}`);
      return [];
    }
  }

  static clearCache(): void {
    this.promptCache.clear();
  }

  static getCacheSize(): number {
    return this.promptCache.size;
  }
}
